import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![up-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNzggNTU1aC00Ni45Yy0xMC4yIDAtMTkuOS00LjktMjUuOS0xMy4yTDUxMiA0NjAuNCA0MDYuOCA2MDUuOGMtNiA4LjMtMTUuNiAxMy4yLTI1LjkgMTMuMkgzMzRjLTYuNSAwLTEwLjMtNy40LTYuNS0xMi43bDE3OC0yNDZjMy4yLTQuNCA5LjctNC40IDEyLjkgMGwxNzggMjQ2YzMuOSA1LjMuMSAxMi43LTYuNCAxMi43eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
