import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![x](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNODIzLjExIDkxMkgyMDAuOUE4OC45IDg4LjkgMCAwMTExMiA4MjMuMTFWMjAwLjlBODguOSA4OC45IDAgMDEyMDAuODkgMTEySDgyMy4xQTg4LjkgODguOSAwIDAxOTEyIDIwMC44OVY4MjMuMUE4OC45IDg4LjkgMCAwMTgyMy4xMSA5MTIiIC8+PHBhdGggZD0iTTc0MCA3MzVINTk2Ljk0TDI4NiAyOTFoMTQzLjA2em0tMTI2LjAxLTM3LjY1aDU2Ljk2TDQxMiAzMjguNjVoLTU2Ljk2eiIgZmlsbC1ydWxlPSJub256ZXJvIiAvPjxwYXRoIGQ9Ik0zMzEuMyA3MzVMNDkxIDU0OS43MyA0NzAuMTEgNTIyIDI4NiA3MzV6TTUyMSA0NjAuMzlMNTQxLjIxIDQ4OSA3MTUgMjg5aC00NC42N3oiIGZpbGwtcnVsZT0ibm9uemVybyIgLz48L2c+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
