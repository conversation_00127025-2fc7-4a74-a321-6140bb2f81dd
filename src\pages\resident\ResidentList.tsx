import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  Row,
  Col,
  Typography,
  App,
  Tabs,
  Statistic,
  Progress,
  Badge,
  Dropdown,
  DatePicker,
  Descriptions,
  Avatar,
  Divider,
  Empty,
  Alert,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  UserAddOutlined,
  TeamOutlined,
  HeartOutlined,
  MedicineBoxOutlined,
  FileTextOutlined,
  MoreOutlined,
  ExportOutlined,
  FilterOutlined,
  CalendarOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined,
  FireOutlined,
  ThunderboltOutlined,
  StarOutlined,
  UserOutlined,
  WarningOutlined,
  FileProtectOutlined,
  SyncOutlined,
  Bar<PERSON><PERSON>Outlined,
  PieChartOutlined,
  SwapOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Option } = Select;
const { Text, Title } = Typography;
const { RangePicker } = DatePicker;

// 居民信息定义
interface Resident {
  id: string;
  name: string;
  gender: '男' | '女';
  age: number;
  phone: string;
  idCard: string;
  address: string;
  diseases: string[];
  managementStatus: 'active' | 'dropout' | 'archived' | 'death';
  lastVisitDate: string;
  nextVisitDate: string;
  institution: string;
  doctor: string;
  riskLevel: 'low' | 'medium' | 'high';
  archiveReason?: string;
  createTime: string;
  followUpCount: number;
  complianceRate: number;
}

// 疾病类型定义
interface Disease {
  key: string;
  name: string;
  color: string;
  icon: React.ReactNode;
  description: string;
}

const ResidentList: React.FC = () => {
  const navigate = useNavigate();
  const { message } = App.useApp();
  const [activeTab, setActiveTab] = useState('active');
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedDisease, setSelectedDisease] = useState<string>('all');
  const [selectedRiskLevel, setSelectedRiskLevel] = useState<string>('all');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [viewingResident, setViewingResident] = useState<Resident | null>(null);
  const [archiveModalVisible, setArchiveModalVisible] = useState(false);
  const [followUpModalVisible, setFollowUpModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingResident, setEditingResident] = useState<Resident | null>(null);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [diseaseChartType, setDiseaseChartType] = useState<'bar' | 'pie'>('bar');
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [addForm] = Form.useForm();

  // 添加错误边界和调试信息
  useEffect(() => {
    console.log('ResidentList component mounted');
    return () => {
      console.log('ResidentList component unmounted');
    };
  }, []);

  // 疾病配置
  const diseases: Disease[] = [
    { key: 'hypertension', name: '高血压', color: '#ff4d4f', icon: <HeartOutlined />, description: '收缩压≥140mmHg或舒张压≥90mmHg' },
    { key: 'diabetes', name: '糖尿病', color: '#fa8c16', icon: <MedicineBoxOutlined />, description: '空腹血糖≥7.0mmol/L或餐后2h血糖≥11.1mmol/L' },
    { key: 'coronary', name: '冠心病', color: '#f759ab', icon: <HeartOutlined />, description: '冠状动脉粥样硬化性心脏病' },
    { key: 'copd', name: '慢阻肺', color: '#13c2c2', icon: <MedicineBoxOutlined />, description: '慢性阻塞性肺疾病' },
    { key: 'stroke', name: '脑卒中', color: '#722ed1', icon: <AlertOutlined />, description: '脑血管意外' },
    { key: 'kidney', name: '慢性肾病', color: '#52c41a', icon: <MedicineBoxOutlined />, description: '慢性肾脏疾病' }
  ];

  // 模拟数据
  const [activeResidents] = useState<Resident[]>([
    {
      id: '1',
      name: '张三',
      gender: '男',
      age: 65,
      phone: '13800138001',
      idCard: '110101195901011234',
      address: '北京市朝阳区三里屯街道',
      diseases: ['hypertension', 'diabetes'],
      managementStatus: 'active',
      lastVisitDate: '2024-01-15',
      nextVisitDate: '2024-02-15',
      institution: '朝阳区人民医院',
      doctor: '李医生',
      riskLevel: 'medium',
      createTime: '2023-06-15 10:30:00',
      followUpCount: 12,
      complianceRate: 85
    },
    {
      id: '2',
      name: '李四',
      gender: '女',
      age: 58,
      phone: '13800138002',
      idCard: '110101196601011234',
      address: '北京市海淀区中关村街道',
      diseases: ['hypertension'],
      managementStatus: 'active',
      lastVisitDate: '2024-01-10',
      nextVisitDate: '2024-02-10',
      institution: '海淀区中医院',
      doctor: '王医生',
      riskLevel: 'low',
      createTime: '2023-08-20 14:20:00',
      followUpCount: 8,
      complianceRate: 92
    }
  ]);

  const [dropoutResidents] = useState<Resident[]>([
    {
      id: '3',
      name: '王五',
      gender: '男',
      age: 72,
      phone: '13800138003',
      idCard: '110101195201011234',
      address: '北京市西城区金融街',
      diseases: ['diabetes', 'coronary'],
      managementStatus: 'dropout',
      lastVisitDate: '2023-07-15',
      nextVisitDate: '2023-08-15',
      institution: '西城区医院',
      doctor: '赵医生',
      riskLevel: 'high',
      createTime: '2023-01-10 16:45:00',
      followUpCount: 6,
      complianceRate: 45
    }
  ]);

  const [archivedResidents] = useState<Resident[]>([
    {
      id: '4',
      name: '赵六',
      gender: '女',
      age: 68,
      phone: '13800138004',
      idCard: '110101195601011234',
      address: '北京市东城区王府井',
      diseases: ['stroke'],
      managementStatus: 'archived',
      lastVisitDate: '2023-12-01',
      nextVisitDate: '2024-01-01',
      institution: '东城区医院',
      doctor: '孙医生',
      riskLevel: 'medium',
      archiveReason: '迁出本地区',
      createTime: '2023-03-15 11:15:00',
      followUpCount: 10,
      complianceRate: 78
    }
  ]);

  // 获取管理状态标签
  const getManagementStatusTag = (status: string) => {
    const statusMap = {
      'active': { color: 'success', text: '在管', icon: <CheckCircleOutlined /> },
      'dropout': { color: 'warning', text: '脱落', icon: <WarningOutlined /> },
      'archived': { color: 'default', text: '归档', icon: <FileProtectOutlined /> },
      'death': { color: 'error', text: '死亡', icon: <ExclamationCircleOutlined /> }
    };
    const config = statusMap[status as keyof typeof statusMap];
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 获取风险等级标签
  const getRiskLevelTag = (level: string) => {
    const levelMap = {
      'low': { color: 'success', text: '低风险', icon: <CheckCircleOutlined /> },
      'medium': { color: 'warning', text: '中风险', icon: <ClockCircleOutlined /> },
      'high': { color: 'error', text: '高风险', icon: <ExclamationCircleOutlined /> }
    };
    const config = levelMap[level as keyof typeof levelMap];
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 获取疾病标签
  const getDiseaseTag = (diseaseKey: string) => {
    const disease = diseases.find(d => d.key === diseaseKey);
    if (!disease) return null;
    return (
      <Tag color={disease.color} icon={disease.icon} key={diseaseKey}>
        {disease.name}
      </Tag>
    );
  };

  // 查看详情
  const handleViewDetail = (resident: Resident) => {
    setViewingResident(resident);
    setDetailModalVisible(true);
  };

  // 编辑操作
  const handleEdit = (resident: Resident) => {
    setEditingResident(resident);
    editForm.setFieldsValue({
      name: resident.name,
      gender: resident.gender,
      age: resident.age,
      phone: resident.phone,
      idCard: resident.idCard,
      address: resident.address,
      diseases: resident.diseases,
      riskLevel: resident.riskLevel,
      doctor: resident.doctor,
      institution: resident.institution
    });
    setEditModalVisible(true);
  };

  // 获取当前标签页的居民数据
  const getCurrentResidents = () => {
    switch (activeTab) {
      case 'active':
        return activeResidents;
      case 'dropout':
        return dropoutResidents;
      case 'archived':
        return archivedResidents;
      default:
        return [];
    }
  };

  // 统计数据
  const getStatistics = () => {
    try {
      const total = activeResidents.length + dropoutResidents.length + archivedResidents.length;
      const active = activeResidents.length;
      const dropout = dropoutResidents.length;
      const archived = archivedResidents.length;

      return { total, active, dropout, archived };
    } catch (error) {
      console.error('Error in getStatistics:', error);
      return { total: 0, active: 0, dropout: 0, archived: 0 };
    }
  };

  const statistics = getStatistics();

  return (
    <div style={{ padding: '0' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Typography.Title level={2} style={{ margin: 0 }}>
          居民管理
        </Typography.Title>
        <Typography.Text type="secondary">
          管理慢性病患者的基本信息和健康档案
        </Typography.Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: 'linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(54, 207, 201, 0.1))',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(24, 144, 255, 0.2)',
            borderRadius: '12px'
          }}>
            <Statistic
              title="总纳管人数"
              value={statistics.total}
              prefix={<TrophyOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: 'linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(135, 208, 104, 0.1))',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(82, 196, 26, 0.2)',
            borderRadius: '12px'
          }}>
            <Statistic
              title="在管患者"
              value={statistics.active}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: 'linear-gradient(135deg, rgba(250, 173, 20, 0.1), rgba(255, 197, 61, 0.1))',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(250, 173, 20, 0.2)',
            borderRadius: '12px'
          }}>
            <Statistic
              title="脱落患者"
              value={statistics.dropout}
              prefix={<WarningOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card style={{
            background: 'linear-gradient(135deg, rgba(140, 140, 140, 0.1), rgba(177, 177, 177, 0.1))',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(140, 140, 140, 0.2)',
            borderRadius: '12px'
          }}>
            <Statistic
              title="归档患者"
              value={statistics.archived}
              prefix={<FileProtectOutlined style={{ color: '#8c8c8c' }} />}
              valueStyle={{ color: '#8c8c8c', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 居民列表表格 */}
      <Card
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
          border: '1px solid #e8f4fd'
        }}
      >
        {/* 标签页 */}
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          style={{ marginBottom: '16px' }}
          items={[
            {
              key: 'active',
              label: (
                <Space>
                  <div style={{
                    padding: '4px 8px',
                    background: 'linear-gradient(135deg, #52c41a, #73d13d)',
                    borderRadius: '6px',
                    color: '#fff'
                  }}>
                    <CheckCircleOutlined />
                  </div>
                  <span style={{ fontWeight: 500 }}>在管患者</span>
                  <Badge count={activeResidents.length} style={{ backgroundColor: '#52c41a' }} />
                </Space>
              )
            },
            {
              key: 'dropout',
              label: (
                <Space>
                  <div style={{
                    padding: '4px 8px',
                    background: 'linear-gradient(135deg, #faad14, #ffc53d)',
                    borderRadius: '6px',
                    color: '#fff'
                  }}>
                    <WarningOutlined />
                  </div>
                  <span style={{ fontWeight: 500 }}>脱落患者</span>
                  <Badge count={dropoutResidents.length} style={{ backgroundColor: '#faad14' }} />
                </Space>
              )
            },
            {
              key: 'archived',
              label: (
                <Space>
                  <div style={{
                    padding: '4px 8px',
                    background: 'linear-gradient(135deg, #8c8c8c, #bfbfbf)',
                    borderRadius: '6px',
                    color: '#fff'
                  }}>
                    <FileProtectOutlined />
                  </div>
                  <span style={{ fontWeight: 500 }}>归档患者</span>
                  <Badge count={archivedResidents.length} style={{ backgroundColor: '#8c8c8c' }} />
                </Space>
              )
            }
          ]}
        />

        {/* 表格 */}
        <Table
          dataSource={getCurrentResidents()}
          columns={[
            {
              title: '姓名',
              dataIndex: 'name',
              key: 'name',
              width: 100,
              align: 'center',
              render: (text, record) => (
                <div style={{ textAlign: 'center' }}>
                  <Space>
                    <Avatar size="small" icon={<UserOutlined />} />
                    <span>{text}</span>
                  </Space>
                </div>
              )
            },
            {
              title: '基本信息',
              key: 'basicInfo',
              width: 150,
              align: 'center',
              render: (_, record) => (
                <div style={{ textAlign: 'center' }}>
                  <div>{record.gender} · {record.age}岁</div>
                  <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                    {record.phone}
                  </Typography.Text>
                </div>
              )
            },
            {
              title: '疾病',
              dataIndex: 'diseases',
              key: 'diseases',
              width: 200,
              align: 'center',
              render: (diseases: string[]) => (
                <div style={{ textAlign: 'center' }}>
                  <Space wrap>
                    {diseases.map(disease => getDiseaseTag(disease))}
                  </Space>
                </div>
              )
            },
            {
              title: '管理状态',
              dataIndex: 'managementStatus',
              key: 'managementStatus',
              width: 100,
              align: 'center',
              render: (status) => (
                <div style={{ textAlign: 'center' }}>
                  {getManagementStatusTag(status)}
                </div>
              )
            },
            {
              title: '风险等级',
              dataIndex: 'riskLevel',
              key: 'riskLevel',
              width: 100,
              align: 'center',
              render: (level) => (
                <div style={{ textAlign: 'center' }}>
                  {getRiskLevelTag(level)}
                </div>
              )
            },
            {
              title: '操作',
              key: 'action',
              width: 200,
              align: 'center',
              render: (_, record) => (
                <Space size="small">
                  <Tooltip title="查看详情">
                    <Button
                      type="primary"
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => handleViewDetail(record)}
                      style={{
                        borderRadius: '6px',
                        background: 'linear-gradient(135deg, #1890ff, #36cfc9)',
                        border: 'none'
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="编辑信息">
                    <Button
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => handleEdit(record)}
                      style={{ borderRadius: '6px' }}
                    />
                  </Tooltip>
                </Space>
              )
            }
          ]}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个患者`
          }}
          size="middle"
          style={{
            borderRadius: '8px',
            overflow: 'hidden'
          }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title={
          <div style={{
            padding: '8px 0',
            borderBottom: '1px solid #f0f0f0',
            marginBottom: '16px'
          }}>
            <Space>
              <Avatar size={40} icon={<UserOutlined />} />
              <div>
                <Title level={4} style={{ margin: 0 }}>
                  {viewingResident?.name}
                </Title>
                <Text type="secondary">
                  {viewingResident?.gender} · {viewingResident?.age}岁
                </Text>
              </div>
            </Space>
          </div>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)} style={{ borderRadius: '8px' }}>
            关闭
          </Button>,
          <Button
            key="edit"
            type="primary"
            style={{
              borderRadius: '8px',
              background: 'linear-gradient(135deg, #1890ff, #36cfc9)',
              border: 'none'
            }}
            onClick={() => {
              setDetailModalVisible(false);
              if (viewingResident) {
                handleEdit(viewingResident);
              }
            }}
          >
            编辑信息
          </Button>
        ]}
        width={800}
        style={{ top: 50 }}
      >
        {viewingResident && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="姓名">{viewingResident.name}</Descriptions.Item>
            <Descriptions.Item label="性别">{viewingResident.gender}</Descriptions.Item>
            <Descriptions.Item label="年龄">{viewingResident.age}岁</Descriptions.Item>
            <Descriptions.Item label="电话">{viewingResident.phone}</Descriptions.Item>
            <Descriptions.Item label="身份证" span={2}>{viewingResident.idCard}</Descriptions.Item>
            <Descriptions.Item label="地址" span={2}>{viewingResident.address}</Descriptions.Item>
            <Descriptions.Item label="疾病">
              <Space wrap>
                {viewingResident.diseases.map(disease => getDiseaseTag(disease))}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="管理状态">
              {getManagementStatusTag(viewingResident.managementStatus)}
            </Descriptions.Item>
            <Descriptions.Item label="风险等级">
              {getRiskLevelTag(viewingResident.riskLevel)}
            </Descriptions.Item>
            <Descriptions.Item label="随访次数">{viewingResident.followUpCount}次</Descriptions.Item>
            <Descriptions.Item label="依从性">{viewingResident.complianceRate}%</Descriptions.Item>
            <Descriptions.Item label="最后就诊">{viewingResident.lastVisitDate}</Descriptions.Item>
            <Descriptions.Item label="下次随访">{viewingResident.nextVisitDate}</Descriptions.Item>
            <Descriptions.Item label="管理医生">{viewingResident.doctor}</Descriptions.Item>
            <Descriptions.Item label="所属机构">{viewingResident.institution}</Descriptions.Item>
            <Descriptions.Item label="建档时间" span={2}>{viewingResident.createTime}</Descriptions.Item>
            {viewingResident.archiveReason && (
              <Descriptions.Item label="归档原因" span={2}>{viewingResident.archiveReason}</Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default ResidentList;
