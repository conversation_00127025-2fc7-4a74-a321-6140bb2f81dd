import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { Layout, Menu, Button, Avatar, Dropdown, message, Badge, Tooltip, App as AntdApp } from 'antd';
import type { MenuProps } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  HomeOutlined,
  SettingOutlined,
  MedicineBoxOutlined,
  BankOutlined,
  SafetyOutlined,
  KeyOutlined,
  LogoutOutlined,
  UserOutlined,
  TeamOutlined,
  ContactsOutlined,
  CalendarOutlined,
  ToolOutlined,
  ClockCircleOutlined,
  RobotOutlined,
  SwapOutlined,
  SendOutlined,
  InboxOutlined,
  HistoryOutlined,
  BellOutlined,
  PieChartOutlined,
  LineChartOutlined,
  FileTextOutlined
} from '@ant-design/icons';

// 页面组件导入
import Login from '@/pages/Login';
import Home from '@/pages/Home';
import SystemManagement from '@/pages/SystemManagement';
import ScreeningManagement from '@/pages/ScreeningManagement';
import ResidentManagement from '@/pages/ResidentManagement';
import ResidentList from '@/pages/resident/ResidentList';
import ResidentListTest from '@/pages/resident/ResidentListTest';
import ChronicDiseaseRecord from './pages/ChronicDiseaseRecord';
import DoctorManagement from '@/pages/system/DoctorManagement';
import InstitutionManagement from '@/pages/system/InstitutionManagement';
import RoleManagement from '@/pages/system/RoleManagement';
import PermissionManagement from '@/pages/system/PermissionManagement';
import FollowUpManagement from '@/pages/FollowUpManagement';
import FollowUpTemplates from '@/pages/followup/FollowUpTemplates';
import FollowUpTaskCenter from '@/pages/followup/FollowUpTaskCenter';
import HealthPrescriptionCenter from '@/pages/followup/HealthPrescriptionCenter';
import ReferralScreening from '@/pages/referral/ReferralScreening';
import ReferralOutbound from '@/pages/referral/ReferralOutbound';
import ReferralInbound from '@/pages/referral/ReferralInbound';
import ReferralRecords from '@/pages/referral/ReferralRecords';
import DiseaseReport from '@/pages/DiseaseReport';
import ChronicDiseaseManagementReport from '@/pages/ChronicDiseaseManagementReport';
import FollowUpStatisticsReport from '@/pages/FollowUpStatisticsReport';
import NotificationManagement from '@/pages/NotificationManagement';

import Breadcrumb from '@/components/Breadcrumb';

const { Header, Sider, Content } = Layout;

// 菜单配置
const menuItems: MenuProps['items'] = [
  {
    key: '/',
    icon: <HomeOutlined />,
    label: '首页',
  },

  {
    key: '/screening',
    icon: <TeamOutlined />,
    label: '筛查管理',
  },
  {
    key: '/resident',
    icon: <ContactsOutlined />,
    label: '居民管理',
    children: [
      { key: '/resident/list', icon: <UserOutlined />, label: '居民列表' },
      { key: '/resident/chronic-disease', icon: <MedicineBoxOutlined />, label: '慢病档案' },
    ],
  },
  {
    key: '/followup',
    icon: <CalendarOutlined />,
    label: '随访管理',
    children: [
      { key: '/followup/tasks', icon: <ClockCircleOutlined />, label: '随访任务中心' },
      { key: '/followup/templates', icon: <ToolOutlined />, label: '随访模板库' },
      { key: '/followup/prescriptions', icon: <RobotOutlined />, label: '健康处方中心' },
    ],
  },
  {
    key: '/referral',
    icon: <SwapOutlined />,
    label: '转诊管理',
    children: [
      { key: '/referral/screening', icon: <TeamOutlined />, label: '患者智能筛选' },
      { key: '/referral/outbound', icon: <SendOutlined />, label: '转出申请管理' },
      { key: '/referral/inbound', icon: <InboxOutlined />, label: '转入接收管理' },
      { key: '/referral/records', icon: <HistoryOutlined />, label: '转诊记录查询' },
    ],
  },
  {
    key: '/reports',
    icon: <PieChartOutlined />,
    label: '报表统计',
    children: [
      { key: '/reports/disease', icon: <PieChartOutlined />, label: '病种报表' },
      { key: '/reports/chronic-management', icon: <LineChartOutlined />, label: '慢病管理报表' },
      { key: '/reports/followup-statistics', icon: <FileTextOutlined />, label: '随访统计报表' },
    ],
  },
  {
    key: '/system',
    icon: <SettingOutlined />,
    label: '系统管理',
    children: [
      { key: '/system/doctors', icon: <MedicineBoxOutlined />, label: '医生管理' },
      { key: '/system/institutions', icon: <BankOutlined />, label: '机构管理' },
      { key: '/system/roles', icon: <SafetyOutlined />, label: '角色管理' },
      { key: '/system/permissions', icon: <KeyOutlined />, label: '权限管理' },
    ],
  },
];

// 主布局组件
const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [unreadCount, setUnreadCount] = useState(3); // 未读通知数量
  const [openKeys, setOpenKeys] = useState<string[]>([]); // 展开的菜单项
  const navigate = useNavigate();
  const location = useLocation();

  // 获取用户信息
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      localStorage.removeItem('isLoggedIn');
      localStorage.removeItem('userInfo');
      message.success('退出登录成功！');
      navigate('/login');
    }
  };

  // 处理通知按钮点击
  const handleNotificationClick = () => {
    navigate('/notifications');
    setUnreadCount(0); // 点击后清除未读数量
  };

  // 用户下拉菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path.startsWith('/system/')) {
      return [path];
    }
    if (path.startsWith('/resident/')) {
      return [path];
    }
    if (path.startsWith('/followup/')) {
      return [path];
    }
    if (path.startsWith('/referral/')) {
      return [path];
    }
    if (path.startsWith('/reports/')) {
      return [path];
    }
    return [path];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const path = location.pathname;
    if (path.startsWith('/system/')) {
      return ['/system'];
    }
    if (path.startsWith('/resident/')) {
      return ['/resident'];
    }
    if (path.startsWith('/followup/')) {
      return ['/followup'];
    }
    if (path.startsWith('/referral/')) {
      return ['/referral'];
    }
    if (path.startsWith('/reports/')) {
      return ['/reports'];
    }
    return [];
  };

  // 处理菜单展开/收起
  const handleOpenChange = (keys: string[]) => {
    // 获取所有一级菜单的key
    const rootSubmenuKeys = ['/resident', '/followup', '/referral', '/reports', '/system'];
    
    // 找到最新打开的菜单
    const latestOpenKey = keys.find(key => openKeys.indexOf(key) === -1);
    
    if (rootSubmenuKeys.indexOf(latestOpenKey!) === -1) {
      // 如果不是一级菜单，直接设置
      setOpenKeys(keys);
    } else {
      // 如果是一级菜单，只保留最新打开的菜单
      setOpenKeys(latestOpenKey ? [latestOpenKey] : []);
    }
  };

  // 初始化展开的菜单项
  useEffect(() => {
    setOpenKeys(getOpenKeys());
  }, [location.pathname]);

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed} 
        style={{ 
          background: '#fff',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 100,
          height: '100vh',
          overflow: 'auto'
        }}
      >
        {/* Logo区域 */}
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: '#f0f2f5',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#1890ff',
          fontWeight: 'bold'
        }}>
          {collapsed ? 'CMS' : '慢性病管理系统'}
        </div>
        
        {/* 菜单 */}
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          openKeys={openKeys}
          onOpenChange={handleOpenChange}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ 
            background: '#fff',
            border: 'none'
          }}
        />
      </Sider>

      <Layout style={{ marginLeft: collapsed ? 80 : 200 }}>
        {/* 顶部标题栏 */}
        <Header style={{ 
          padding: '0 16px', 
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 1px 4px rgba(0,21,41,.08)'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* 通知按钮 */}
            <Tooltip title="通知管理">
              <Badge count={unreadCount} size="small">
                <Button
                  type="text"
                  icon={<BellOutlined />}
                  onClick={handleNotificationClick}
                  style={{
                    fontSize: '16px',
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '8px',
                    transition: 'all 0.3s',
                    color: '#666'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f0f0f0';
                    e.currentTarget.style.color = '#1890ff';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#666';
                  }}
                />
              </Badge>
            </Tooltip>
            
            {/* 用户头像下拉菜单 */}
            <Dropdown 
              menu={{ 
                items: userMenuItems,
                onClick: handleUserMenuClick
              }} 
              placement="bottomRight"
            >
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                cursor: 'pointer',
                padding: '0 12px',
                borderRadius: '6px',
                transition: 'background-color 0.3s'
              }}>
                <Avatar 
                  size="small" 
                  icon={<UserOutlined />} 
                  style={{ marginRight: '8px' }}
                />
                <span>{userInfo.name || '管理员'}</span>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 主内容区域 */}
        <Content style={{
          background: 'transparent',
          margin: '16px',
          minHeight: 'calc(100vh - 112px)'
        }}>
          <Breadcrumb />
          <Routes>
            <Route path="/" element={<Home />} />

            <Route path="/screening" element={<ScreeningManagement />} />
            <Route path="/resident" element={<ResidentManagement />} />
            <Route path="/resident/list" element={<ResidentList />} />
            <Route path="/resident/chronic-disease" element={<ChronicDiseaseRecord />} />
            <Route path="/followup" element={<FollowUpManagement />} />
            <Route path="/followup/tasks" element={<FollowUpTaskCenter />} />
            <Route path="/followup/templates" element={<FollowUpTemplates />} />
            <Route path="/followup/prescriptions" element={<HealthPrescriptionCenter />} />
            <Route path="/referral" element={<Navigate to="/referral/screening" replace />} />
            <Route path="/referral/screening" element={<ReferralScreening />} />
            <Route path="/referral/outbound" element={<ReferralOutbound />} />
            <Route path="/referral/inbound" element={<ReferralInbound />} />
            <Route path="/referral/records" element={<ReferralRecords />} />
            <Route path="/reports" element={<Navigate to="/reports/disease" replace />} />
            <Route path="/reports/disease" element={<DiseaseReport />} />
            <Route path="/reports/chronic-management" element={<ChronicDiseaseManagementReport />} />
            <Route path="/reports/followup-statistics" element={<FollowUpStatisticsReport />} />
            <Route path="/system" element={<SystemManagement />} />
            <Route path="/system/doctors" element={<DoctorManagement />} />
            <Route path="/system/institutions" element={<InstitutionManagement />} />
            <Route path="/system/roles" element={<RoleManagement />} />
            <Route path="/system/permissions" element={<PermissionManagement />} />
            <Route path="/notifications" element={<NotificationManagement />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
};

// 路由守卫组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
  
  if (!isLoggedIn) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

// 主应用组件
const App: React.FC = () => {
  return (
    <AntdApp>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />

          <Route 
            path="/*" 
            element={
              <ProtectedRoute>
                <MainLayout />
              </ProtectedRoute>
            } 
          />
        </Routes>
      </Router>
    </AntdApp>
  );
};

export default App;